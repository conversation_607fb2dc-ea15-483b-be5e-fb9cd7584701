/* Message Hub Modal Styles */

/* Enhanced close button for better touch interaction */
.message-hub-modal .close {
    background: none !important;
    border: none !important;
    color: #fff !important;
    cursor: pointer !important;
    -webkit-tap-highlight-color: transparent !important;
    touch-action: manipulation !important;
    -webkit-user-select: none !important;
    user-select: none !important;
}

.message-hub-modal .close:hover,
.message-hub-modal .close:focus {
    color: #fff !important;
    opacity: 0.8 !important;
    text-decoration: none !important;
}

/* Mobile modal fixes */
@media only screen and (max-width: 767.98px) {
    /* Mobile modal positioning */
    .mobile-cpt-modal {
        max-width: 95% !important;
        margin: 0 auto;
    }

    .mobile-cpt-modal .modal-content {
        border-radius: 8px;
        overflow: hidden;
    }

    /* Enhanced close button for mobile */
    .mobile-cpt-modal .close {
        padding: 0.75rem !important;
        min-width: 48px !important;
        min-height: 48px !important;
        -webkit-tap-highlight-color: transparent !important;
        touch-action: manipulation !important;
        -webkit-user-select: none !important;
        user-select: none !important;
        cursor: pointer !important;
    }
}

/* Safari specific modal fixes */
@supports (-webkit-appearance: none) {
    /* Safari close button fixes */
    .close {
        -webkit-appearance: none !important;
        appearance: none !important;
    }

    /* Mobile Safari specific fixes */
    @media only screen and (max-width: 767.98px) {
        .mobile-cpt-modal .close {
            -webkit-touch-callout: none !important;
        }
    }
}

/* Additional iOS/Safari modal fixes */
@media only screen and (max-width: 767.98px) {
  /* Ensure modals can be closed on iOS */
  .message-hub-modal .close {
    opacity: 1 !important;
    min-width: 44px !important;
    min-height: 44px !important;
    padding: 10px !important;
    margin: 0 !important;
    position: relative !important;
    z-index: 9999 !important;
    -webkit-tap-highlight-color: rgba(0,0,0,0) !important;
    touch-action: manipulation !important;
  }
  
  /* Ensure modal backdrop can be tapped through */
  .modal-backdrop {
    -webkit-tap-highlight-color: transparent !important;
    touch-action: manipulation !important;
  }
  
  /* Fix for iOS Safari modal stacking */
  .modal {
    -webkit-overflow-scrolling: touch !important;
  }
}
