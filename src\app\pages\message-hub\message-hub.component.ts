import { Component, OnInit } from '@angular/core';
import { MessageHubService } from 'src/app/services/message-hub/message-hub.service';
import { AppComponent } from 'src/app/app.component';
import { CommonService } from 'src/app/services/common/common.service';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { ToastrService } from 'ngx-toastr';
declare let $: any;

@Component({
  selector: 'app-message-hub',
  templateUrl: './message-hub.component.html',
  styleUrls: ['./message-hub.component.css']
})
export class MessageHubComponent implements OnInit {
  public listOfMessages: Array<any> = [];
  public listOfFacilities: Array<any> = [];
  public p: number;
  public searchByName: string;
  public request: any = {};
  public device: boolean = false;
  public navigator = navigator;
  constructor(private readonly mgsServ: MessageHubService, private readonly appComp: AppComponent, private readonly commonServ: CommonService
    , private readonly encrDecr: EncrDecrServiceService, private readonly toastr: ToastrService) { }

  ngOnInit() {
    this.device=this.appComp.device;
    this.appComp.loadPageName('Message Hub', 'messageHubTab');
    this.getMessages();
  }

  // Simplified device detection
  isIPhone(): boolean {
    return /iPhone|iPad|iPod/i.test(navigator.userAgent);
  }

  isSafari(): boolean {
    const userAgent = navigator.userAgent;
    return /Safari/i.test(userAgent) && !/Chrome/i.test(userAgent) && !/Chromium/i.test(userAgent);
  }

  isIPhoneOrSafari(): boolean {
    return this.isIPhone() || this.isSafari();
  }

  getMessages() {
    this.commonServ.startLoading();
    this.mgsServ.getMessages().subscribe((p: any) => {
      this.listOfMessages = p;
      this.commonServ.stopLoading();
    }, error => { this.commonServ.stopLoading(); });
  }

  markAllAsRead() {
    this.commonServ.startLoading();
    this.request.sGROUP_ID = this.encrDecr.set('0');
    this.request.FLAG = this.encrDecr.set('ALL');
    this.mgsServ.markAsAllRead(this.request).subscribe((p: any) => {
      this.request = {};
      this.mgsServ.getMessages().subscribe((p: any) => {
        this.listOfMessages = p;
      }, error => { });
      this.commonServ.stopLoading();
    }, error => {
      this.request = {};
      this.commonServ.stopLoading();
    });
  }

  markAsRead(groupId, isRead) {
    if (!isRead) {
      this.request.sGROUP_ID = this.encrDecr.set(groupId);
      this.request.FLAG = this.encrDecr.set('BADGE');
      this.mgsServ.markAsAllRead(this.request).subscribe((p: any) => {
        this.request = {};
      }, error => {
        this.request = {};
      });
    }

    const modalId = "#readMsg-" + groupId;

    // Simplified modal opening - let Bootstrap handle it for non-problematic browsers
    if (this.isIPhoneOrSafari()) {
      // For iPhone and Safari, use custom modal opening
      this.openModalSafely(modalId);
    } else {
      // Standard Bootstrap modal for other browsers
      $(modalId).modal('show');
    }
  }

  // Simplified modal opening for iPhone and Safari
  openModalSafely(modalId: string) {
    // Clean up any existing modals first
    $('.modal-backdrop').remove();
    $('body').removeClass('modal-open');
    $('.modal').hide();

    // Show the modal
    $(modalId).modal('show');
  }



  archiveMessage(groupId) {
    this.commonServ.startLoading();
    this.request.sGROUPID = this.encrDecr.set(groupId.toString());
    this.mgsServ.archiveMessage(this.request).subscribe((p: any) => {
      this.request = {};
      if (p > 0) {
        this.mgsServ.getMessages().subscribe((p: any) => {
          this.listOfMessages = p;
        }, error => { });
        this.commonServ.stopLoading();
      }
    }, error => {
      this.request = {};
      this.commonServ.stopLoading();
    });
  }

  closeReadMgs(id, isRead, event?: Event) {
    const modalId = "#readMsg-" + id;

    // Prevent event bubbling and default behavior
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    // For iPhone and Safari, use our enhanced safe closing method
    if (this.isIPhoneOrSafari()) {
      this.closeModalSafely(modalId);
    } else {
      // Standard Bootstrap modal hide for other browsers
      $(modalId).modal('hide');
    }

    if (!isRead) {
      this.mgsServ.getMessages().subscribe((p: any) => {
        this.listOfMessages = p;
      }, error => { });
    }
  }

  closeModalSafely(modalId: string) {
    console.log('Closing modal safely for iPhone/Safari:', modalId);
    
    try {
      // 1. First blur any active element inside the modal
      if (document.activeElement instanceof HTMLElement && 
          $(modalId).find(document.activeElement).length > 0) {
        document.activeElement.blur();
      }
      
      // 2. Move focus to body before hiding modal
      document.body.focus();
      
      // 3. Remove modal from accessibility tree before hiding
      $(modalId).find('button, a, input, select, textarea, [tabindex]')
        .attr('tabindex', '-1')
        .prop('disabled', true);
      
      // 4. Force immediate modal hide with all possible methods
      $(modalId).modal('hide');
      $(modalId).removeClass('show');
      $(modalId).attr('aria-hidden', 'true');
      $(modalId).css('display', 'none');
      $(modalId).css('z-index', '-1');
      
      // 5. Aggressively remove backdrop and reset body
      $('.modal-backdrop').remove();
      $('body').removeClass('modal-open');
      $('body').css({
        'overflow': '',
        'padding-right': '',
        'position': '',
        'height': '',
        'width': ''
      });
      
      // 6. Additional cleanup for iOS Safari
      setTimeout(() => {
        $('.modal-backdrop').remove();
        $(modalId).hide();
        
        // Reset body styles completely
        $('body').removeClass('modal-open');
        $('html, body').css({
          'overflow': 'auto',
          'height': 'auto',
          'position': 'static',
          'width': 'auto'
        });
        
        console.log('Modal cleanup completed for:', modalId);
      }, 50);
      
      // 7. Final document-level cleanup
      setTimeout(() => {
        $(document).find('.modal-backdrop').remove();
        $('body').removeClass('modal-open');
      }, 200);
    } catch (err) {
      console.error('Error in closeModalSafely:', err);
      // Fallback method if the above fails
      $('.modal-backdrop').remove();
      $('body').removeClass('modal-open');
      $(modalId).hide();
    }
  }





  openNotes() {
    this.commonServ.startLoading();
    this.commonServ.getFacilities().subscribe((p: any) => {
      this.listOfFacilities = p;
      $('#sendMessagePop').modal('show');
      this.commonServ.stopLoading();
    }, error => { console.error(error.status); });
  }

}
